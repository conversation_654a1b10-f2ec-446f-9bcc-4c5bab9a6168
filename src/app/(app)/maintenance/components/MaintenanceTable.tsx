"use client";

import { MaintenanceTargetType } from "@prisma/client";
import { AiOutlineDelete, AiOutlineWarning } from "react-icons/ai";
import { AgGridReact } from "ag-grid-react";
import type { ColDef, GridOptions, ICellRendererParams } from "ag-grid-community";
import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import "~/styles/ag-theme-eulektro.css";

interface MaintenanceRecord {
  id: string;
  date: string;
  description: string;
  notes?: string;
  targetType: MaintenanceTargetType;
  locationId?: string;
  evseId?: string;
  connectorId?: string;
  nextDueDate?: string;
  category: {
    id: string;
    name: string;
  };
  user: {
    id: string;
    name: string;
    lastName: string;
  };
  location?: {
    id: string;
    name: string;
  };
  evse?: {
    uid: string;
    evse_id: string;
  };
  ou: {
    id: string;
    name: string;
  };
}

interface Props {
  records: MaintenanceRecord[];
  onDelete: (id: string) => void;
}

const MaintenanceTable = ({ records, onDelete }: Props) => {
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("de-DE");
    } catch {
      return dateString;
    }
  };

  const getTargetTypeLabel = (targetType: MaintenanceTargetType) => {
    switch (targetType) {
      case MaintenanceTargetType.LOCATION:
        return "Standort";
      case MaintenanceTargetType.EVSE:
        return "Ladesäule";
      case MaintenanceTargetType.CONNECTOR:
        return "Connector";
      case MaintenanceTargetType.ALL_EVSES:
        return "Alle Ladesäulen des Standorts";
      default:
        return targetType;
    }
  };

  const getTargetDescription = (record: MaintenanceRecord) => {
    switch (record.targetType) {
      case MaintenanceTargetType.LOCATION:
        return record.location?.name || "Unbekannter Standort";
      case MaintenanceTargetType.EVSE:
        return record.evse?.evse_id || "Unbekannte EVSE";
      case MaintenanceTargetType.CONNECTOR:
        return `${record.evse?.evse_id || "Unbekannte EVSE"} - Connector ${record.connectorId || "?"}`;
      case MaintenanceTargetType.ALL_EVSES:
        return `Alle Ladesäulen (${record.location?.name || "Standort"})`;
      default:
        return "-";
    }
  };

  const getCategoryBadgeColor = (categoryName: string) => {
    switch (categoryName) {
      case "DGUV-V3":
        return "bg-red-100 text-red-800";
      case "Shutter":
        return "bg-blue-100 text-blue-800";
      case "RFID":
        return "bg-green-100 text-green-800";
      case "Ethernet":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const isDueSoon = (nextDueDate?: string) => {
    if (!nextDueDate) return false;
    const dueDate = new Date(nextDueDate);
    const now = new Date();
    const diffInDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffInDays <= 30 && diffInDays >= 0; // Due within 30 days
  };

  const isOverdue = (nextDueDate?: string) => {
    if (!nextDueDate) return false;
    const dueDate = new Date(nextDueDate);
    const now = new Date();
    return dueDate < now;
  };

  // Cell Renderers
  const DateCellRenderer = (params: ICellRendererParams) => {
    return formatDate(params.value);
  };

  const CategoryCellRenderer = (params: ICellRendererParams) => {
    const categoryName = params.data?.category?.name || "";
    const badgeColor = getCategoryBadgeColor(categoryName);
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${badgeColor}`}>
        {categoryName}
      </span>
    );
  };

  const TargetTypeCellRenderer = (params: ICellRendererParams) => {
    return getTargetTypeLabel(params.value);
  };

  const TargetDescriptionCellRenderer = (params: ICellRendererParams) => {
    return getTargetDescription(params.data);
  };

  const DescriptionCellRenderer = (params: ICellRendererParams) => {
    const record = params.data;
    return (
      <div className="max-w-xs">
        <div className="truncate" title={record.description}>
          {record.description}
        </div>
        {record.notes && (
          <div className="text-xs text-gray-500 truncate" title={record.notes}>
            {record.notes}
          </div>
        )}
      </div>
    );
  };

  const UserCellRenderer = (params: ICellRendererParams) => {
    const user = params.data?.user;
    return user ? `${user.name} ${user.lastName}` : "-";
  };

  const NextDueDateCellRenderer = (params: ICellRendererParams) => {
    const nextDueDate = params.value;
    if (!nextDueDate) {
      return <span className="text-gray-400">-</span>;
    }

    const dueSoon = isDueSoon(nextDueDate);
    const overdue = isOverdue(nextDueDate);

    return (
      <div className="flex items-center gap-1">
        {overdue && <AiOutlineWarning className="text-red-500" size={16} />}
        {dueSoon && !overdue && <AiOutlineWarning className="text-yellow-500" size={16} />}
        <span
          className={
            overdue
              ? "text-red-600 font-medium"
              : dueSoon
              ? "text-yellow-600 font-medium"
              : "text-gray-900"
          }
        >
          {formatDate(nextDueDate)}
        </span>
      </div>
    );
  };

  const ActionCellRenderer = (params: ICellRendererParams) => {
    return (
      <button
        onClick={() => onDelete(params.data.id)}
        className="text-red-600 hover:text-red-900"
        title="Löschen"
      >
        <AiOutlineDelete size={16} />
      </button>
    );
  };

  if (records.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        Keine Wartungseinträge vorhanden.
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Datum
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Kategorie
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Bereich
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Ziel
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Beschreibung
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Durchgeführt von
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Nächste Prüfung
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Aktionen
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {records.map((record) => (
            <tr key={record.id} className="hover:bg-gray-50">
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                {formatDate(record.date)}
              </td>
              <td className="px-4 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getCategoryBadgeColor(record.category.name)}`}>
                  {record.category.name}
                </span>
              </td>
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                {getTargetTypeLabel(record.targetType)}
              </td>
              <td className="px-4 py-4 text-sm text-gray-900">
                <div className="max-w-xs truncate" title={getTargetDescription(record)}>
                  {getTargetDescription(record)}
                </div>
              </td>
              <td className="px-4 py-4 text-sm text-gray-900">
                <div className="max-w-xs">
                  <div className="truncate" title={record.description}>
                    {record.description}
                  </div>
                  {record.notes && (
                    <div className="text-xs text-gray-500 truncate" title={record.notes}>
                      {record.notes}
                    </div>
                  )}
                </div>
              </td>
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                {record.user.name} {record.user.lastName}
              </td>
              <td className="px-4 py-4 whitespace-nowrap text-sm">
                {record.nextDueDate ? (
                  <div className="flex items-center gap-1">
                    {isOverdue(record.nextDueDate) && (
                      <AiOutlineWarning className="text-red-500" size={16} />
                    )}
                    {isDueSoon(record.nextDueDate) && !isOverdue(record.nextDueDate) && (
                      <AiOutlineWarning className="text-yellow-500" size={16} />
                    )}
                    <span className={
                      isOverdue(record.nextDueDate) 
                        ? "text-red-600 font-medium"
                        : isDueSoon(record.nextDueDate)
                        ? "text-yellow-600 font-medium"
                        : "text-gray-900"
                    }>
                      {formatDate(record.nextDueDate)}
                    </span>
                  </div>
                ) : (
                  <span className="text-gray-400">-</span>
                )}
              </td>
              <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  onClick={() => onDelete(record.id)}
                  className="text-red-600 hover:text-red-900"
                  title="Löschen"
                >
                  <AiOutlineDelete size={16} />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default MaintenanceTable;
