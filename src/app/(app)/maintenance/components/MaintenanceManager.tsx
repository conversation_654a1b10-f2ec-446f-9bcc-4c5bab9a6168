"use client";

import { useState, useEffect } from "react";
import type { MaintenanceTargetType } from "@prisma/client";
import MaintenanceTable from "./MaintenanceTable";
import CategoryManager from "./CategoryManager";
import QuickMaintenanceForm from "./QuickMaintenanceForm";
import Button from "~/component/button";
import { AiOutlineSetting } from "react-icons/ai";
import Card from "~/component/card";
import Loading from "~/app/(app)/maintenance/loading";

interface MaintenanceCategory {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
  _count: {
    maintenanceRecords: number;
  };
}

interface MaintenanceRecord {
  id: string;
  date: string;
  description: string;
  notes?: string;
  targetType: MaintenanceTargetType;
  locationId?: string;
  evseId?: string;
  connectorId?: string;
  nextDueDate?: string;
  category: {
    id: string;
    name: string;
  };
  user: {
    id: string;
    name: string;
    lastName: string;
  };
  location?: {
    id: string;
    name: string;
  };
  evse?: {
    uid: string;
    evse_id: string;
  };
  ou: {
    id: string;
    name: string;
  };
}

interface Location {
  id: string;
  name: string;
  city: string;
  street: string;
  evses: {
    uid: string;
    evse_id: string;
    status: string;
  }[];
}

const MaintenanceManager = () => {
  const [showCategoryManager, setShowCategoryManager] = useState(false);
  const [categories, setCategories] = useState<MaintenanceCategory[]>([]);
  const [records, setRecords] = useState<MaintenanceRecord[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLocationId, setSelectedLocationId] = useState("");
  const [selectedEvseId, setSelectedEvseId] = useState("");

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/maintenance/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  // Fetch records
  const fetchRecords = async () => {
    try {
      const response = await fetch("/api/maintenance/records");
      if (response.ok) {
        const data = await response.json();
        setRecords(data.records);
      }
    } catch (error) {
      console.error("Error fetching records:", error);
    }
  };

  // Fetch locations
  const fetchLocations = async () => {
    try {
      const response = await fetch("/api/maintenance/locations");
      if (response.ok) {
        const data = await response.json();
        setLocations(data);
      }
    } catch (error) {
      console.error("Error fetching locations:", error);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      await Promise.all([fetchCategories(), fetchRecords(), fetchLocations()]);
      setLoading(false);
    };

    fetchData();
  }, [refreshTrigger]);

  const handleFormSubmit = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Sind Sie sicher, dass Sie diesen Wartungseintrag löschen möchten?")) {
      return;
    }

    try {
      const response = await fetch(`/api/maintenance/records/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setRefreshTrigger((prev) => prev + 1);
      } else {
        alert("Fehler beim Löschen des Wartungseintrags");
      }
    } catch (error) {
      console.error("Error deleting record:", error);
      alert("Fehler beim Löschen des Wartungseintrags");
    }
  };

  const handleCategoryUpdate = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  if (loading) {
    return <Loading />;
  }

  return (
    <Card>
      {/* Header with action buttons */}
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <h2 className="text-xl font-bold text-primary">Wartung & Reparaturen</h2>
        <div className="flex flex-col gap-2 sm:flex-row">
          <Button
            type="button"
            onClick={() => setShowCategoryManager(true)}
            className="flex items-center gap-2"
          >
            <AiOutlineSetting size={16} />
            Kategorien verwalten
          </Button>
        </div>
      </div>

      {/* Mobile-optimized quick form */}
      <div className="block">
        <QuickMaintenanceForm
          categories={categories}
          locations={locations}
          onSubmit={handleFormSubmit}
        />
      </div>

      {/* Forms and modals */}

      {showCategoryManager && (
        <CategoryManager
          categories={categories}
          onUpdate={handleCategoryUpdate}
          onClose={() => setShowCategoryManager(false)}
        />
      )}

      {/* Records table */}
      <MaintenanceTable records={records} onDelete={handleDelete} />
    </Card>
  );
};

export default MaintenanceManager;
